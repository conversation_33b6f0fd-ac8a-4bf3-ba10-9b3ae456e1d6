{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Signal Record", "description": "Schema for a signal record containing pre-notification data and metadata", "type": "object", "required": ["category", "payload", "signalId", "permafrag", "start", "isn", "publishedDate", "publishedDateTime", "summary", "correlation-id", "end", "predicate", "provider", "object"], "properties": {"category": {"type": "array", "description": "Array of category tags for the signal", "items": {"type": "string"}, "minItems": 1}, "payload": {"type": "object", "description": "The main data payload of the signal", "required": ["mode", "chedNumbers", "countryOf<PERSON><PERSON>in", "unitIdentification", "commodityDescription", "location", "cnCodes", "importerEORI", "exporterEORI"], "properties": {"mode": {"type": "string", "description": "Transport mode", "examples": ["RORO"]}, "chedNumbers": {"type": "array", "description": "Array of CHED (Common Health Entry Document) numbers", "items": {"type": "string", "pattern": "^CHED[A-Z]\\.GB\\.[0-9]{4}\\.[0-9]+$"}, "minItems": 1}, "countryOfOrigin": {"type": "string", "description": "ISO 3166-1 alpha-2 country code of origin", "pattern": "^[A-Z]{2}$"}, "unitIdentification": {"type": "object", "description": "Identification details for the transport unit", "properties": {"trailerRegistrationNumber": {"type": "string", "description": "Registration number of the trailer"}}, "additionalProperties": true}, "commodityDescription": {"type": "string", "description": "Description of the commodity being transported"}, "location": {"type": "string", "description": "Location information, may include coordinates", "pattern": "^.+\\s\\([-+]?[0-9]*\\.?[0-9]+,\\s*[-+]?[0-9]*\\.?[0-9]+\\)$"}, "cnCodes": {"type": "array", "description": "Array of Combined Nomenclature codes", "items": {"type": "string", "pattern": "^[0-9]+$"}, "minItems": 1}, "importerEORI": {"type": "string", "description": "Economic Operators Registration and Identification number of the importer", "pattern": "^[A-Z]{2}[A-Z0-9]+$"}, "exporterEORI": {"type": "string", "description": "Economic Operators Registration and Identification number of the exporter", "pattern": "^[A-Z]{2}[A-Z0-9]+$"}}, "additionalProperties": false}, "signalId": {"type": "string", "description": "Unique identifier for the signal", "format": "uuid"}, "permafrag": {"type": "string", "description": "Permanent fragment identifier", "pattern": "^signals/[0-9]{8}-[a-f0-9]{8}-[a-f0-9]{8}$"}, "start": {"type": "string", "description": "Start timestamp in ISO 8601 format", "format": "date-time"}, "isn": {"type": "string", "description": "Information Sharing Network identifier", "format": "hostname"}, "publishedDate": {"type": "string", "description": "Date when the signal was published", "format": "date"}, "publishedDateTime": {"type": "string", "description": "Timestamp when the signal was published in ISO 8601 format", "format": "date-time"}, "summary": {"type": "string", "description": "Human-readable summary of the signal"}, "correlation-id": {"type": "string", "description": "Correlation identifier for linking related signals", "format": "uuid"}, "end": {"type": "string", "description": "End timestamp in ISO 8601 format", "format": "date-time"}, "predicate": {"type": "string", "description": "Predicate describing the action or state"}, "provider": {"type": "string", "description": "Provider or source of the signal", "format": "hostname"}, "object": {"type": "string", "description": "Object or subject of the signal"}}, "additionalProperties": false}